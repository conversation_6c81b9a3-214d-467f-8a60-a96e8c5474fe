{"format": 1, "restore": {"/Users/<USER>/Documents/FirstClient/Assembly-CSharp-Editor.csproj": {}}, "projects": {"/Users/<USER>/Documents/FirstClient/Assembly-CSharp-Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/FirstClient/Assembly-CSharp-Editor.csproj", "projectName": "Assembly-CSharp-Editor", "projectPath": "/Users/<USER>/Documents/FirstClient/Assembly-CSharp-Editor.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/FirstClient/Temp/obj/Assembly-CSharp-Editor/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"/Users/<USER>/Documents/FirstClient/Assembly-CSharp.csproj": {"projectPath": "/Users/<USER>/Documents/FirstClient/Assembly-CSharp.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.203/RuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Documents/FirstClient/Assembly-CSharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/FirstClient/Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "/Users/<USER>/Documents/FirstClient/Assembly-CSharp.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/FirstClient/Temp/obj/Assembly-CSharp/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.203/RuntimeIdentifierGraph.json"}}}}}
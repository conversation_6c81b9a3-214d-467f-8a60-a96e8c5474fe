Unity Editor version:    6000.0.58f2 (92dee566b325)
Branch:                  6000.0/respin/6000.0.58f2-44b8bf3a32
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.7.1 (Build 24G231)
Darwin version:          24.6.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        65536 MB
Using pre-set license

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/6000.0.58f2/Unity.app/Contents/MacOS/Unity
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
/Users/<USER>/Documents/FirstClient
-logFile
Logs/AssetImportWorker1.log
-srvPort
52290
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
-name AssetImport
Successfully changed project path to: /Users/<USER>/Documents/FirstClient
/Users/<USER>/Documents/FirstClient
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8648384832]  Target information:

Player connection [8648384832]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 803286173 [EditorId] 803286173 [Version] 1048832 [Id] OSXEditor(0,mingyundemaodeMacBook-Pro.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8648384832]  * "[IP] ********** [Port] 0 [Flags] 2 [Guid] 803286173 [EditorId] 803286173 [Version] 1048832 [Id] OSXEditor(0,mingyundemaodeMacBook-Pro.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8648384832] Host joined multi-casting on [***********:54997]...
Player connection [8648384832] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.74 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.58f2 (92dee566b325)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/6000.0.58f2/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/Documents/FirstClient/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
 preferred device: Apple M3 Max (high power)
Metal devices available: 1
0: Apple M3 Max (high power)
Using device Apple M3 Max (high power)
Initializing Metal device caps: Apple M3 Max
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/6000.0.58f2/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/6000.0.58f2/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/6000.0.58f2/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56260
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.58f2/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.58f2/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.58f2/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.58f2/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.58f2/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.004946 seconds.
- Loaded All Assemblies, in  0.260 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 63 ms
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.356 seconds
Domain Reload Profiling: 616ms
	BeginReloadAssembly (75ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (122ms)
		LoadAssemblies (77ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (118ms)
			TypeCache.Refresh (117ms)
				TypeCache.ScanAssembly (109ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (356ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (326ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (159ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (48ms)
			ProcessInitializeOnLoadAttributes (79ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.485 seconds
Refreshing native plugins compatible for Editor in 0.23 ms, found 1 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.515 seconds
Domain Reload Profiling: 1000ms
	BeginReloadAssembly (86ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (343ms)
		LoadAssemblies (210ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (166ms)
			TypeCache.Refresh (130ms)
				TypeCache.ScanAssembly (117ms)
			BuildScriptInfoCaches (25ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (515ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (391ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (110ms)
			ProcessInitializeOnLoadAttributes (229ms)
			ProcessInitializeOnLoadMethodAttributes (35ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Launching external process: /Applications/Unity/Hub/Editor/6000.0.58f2/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.03 seconds
Refreshing native plugins compatible for Editor in 0.30 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6282 unused Assets / (3.9 MB). Loaded Objects now: 6989.
Memory consumption went from 147.1 MB to 143.1 MB.
Total: 5.635375 ms (FindLiveObjects: 0.585917 ms CreateObjectMapping: 0.197875 ms MarkObjects: 3.695375 ms  DeleteObjects: 1.155834 ms)

========================================================================
Received Import Request.
  Time since last request: 910704.521332 seconds.
  path: Assets/TextMesh Pro/Resources/Fonts & Materials/JCYT-500W SDF.asset
  artifactKey: Guid(9521efa1c5b0749efb1d38f92a428103) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Fonts & Materials/JCYT-500W SDF.asset using Guid(9521efa1c5b0749efb1d38f92a428103) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '04c5ad7efb901dd93c65c9ea4e6d10ef') in 0.460954209 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000082 seconds.
  path: Assets/TextMesh Pro/Resources/Fonts & Materials/JCYT-300W SDF.asset
  artifactKey: Guid(245d23ed82e684a2b883d93e7ffc9b31) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Fonts & Materials/JCYT-300W SDF.asset using Guid(245d23ed82e684a2b883d93e7ffc9b31) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'db684d05cd4db16b119ad25f5d5ecfcf') in 0.019441167 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x355a07000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.380 seconds
Refreshing native plugins compatible for Editor in 0.17 ms, found 1 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.445 seconds
Domain Reload Profiling: 828ms
	BeginReloadAssembly (119ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (47ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (219ms)
		LoadAssemblies (138ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (106ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (90ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (445ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (347ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (199ms)
			ProcessInitializeOnLoadMethodAttributes (33ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.26 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6278 unused Assets / (4.0 MB). Loaded Objects now: 7074.
Memory consumption went from 178.6 MB to 174.6 MB.
Total: 4.840500 ms (FindLiveObjects: 0.305459 ms CreateObjectMapping: 0.119125 ms MarkObjects: 3.332541 ms  DeleteObjects: 1.083042 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17ebb3000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.370 seconds
Refreshing native plugins compatible for Editor in 0.19 ms, found 1 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.423 seconds
Domain Reload Profiling: 795ms
	BeginReloadAssembly (104ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (11ms)
	LoadAllAssembliesAndSetupDomain (225ms)
		LoadAssemblies (138ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (110ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (96ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (423ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (337ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (195ms)
			ProcessInitializeOnLoadMethodAttributes (33ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.20 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6278 unused Assets / (3.8 MB). Loaded Objects now: 7076.
Memory consumption went from 173.1 MB to 169.3 MB.
Total: 4.471083 ms (FindLiveObjects: 0.244417 ms CreateObjectMapping: 0.121625 ms MarkObjects: 3.272166 ms  DeleteObjects: 0.832583 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17ebb3000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.372 seconds
Refreshing native plugins compatible for Editor in 0.19 ms, found 1 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.421 seconds
Domain Reload Profiling: 794ms
	BeginReloadAssembly (108ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (223ms)
		LoadAssemblies (140ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (108ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (94ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (421ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (336ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (193ms)
			ProcessInitializeOnLoadMethodAttributes (31ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.20 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6278 unused Assets / (3.8 MB). Loaded Objects now: 7078.
Memory consumption went from 173.1 MB to 169.3 MB.
Total: 4.190125 ms (FindLiveObjects: 0.255042 ms CreateObjectMapping: 0.120958 ms MarkObjects: 2.993416 ms  DeleteObjects: 0.820292 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17ebb3000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.373 seconds
Refreshing native plugins compatible for Editor in 0.20 ms, found 1 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.427 seconds
Domain Reload Profiling: 802ms
	BeginReloadAssembly (109ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (223ms)
		LoadAssemblies (140ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (107ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (94ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (427ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (339ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (193ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.26 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6278 unused Assets / (3.7 MB). Loaded Objects now: 7080.
Memory consumption went from 173.1 MB to 169.4 MB.
Total: 4.833792 ms (FindLiveObjects: 0.306959 ms CreateObjectMapping: 0.121709 ms MarkObjects: 3.465333 ms  DeleteObjects: 0.939584 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x32508b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.369 seconds
Refreshing native plugins compatible for Editor in 0.22 ms, found 1 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.435 seconds
Domain Reload Profiling: 807ms
	BeginReloadAssembly (105ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (222ms)
		LoadAssemblies (138ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (109ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (93ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (436ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (346ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (200ms)
			ProcessInitializeOnLoadMethodAttributes (33ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.22 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6278 unused Assets / (3.4 MB). Loaded Objects now: 7082.
Memory consumption went from 173.1 MB to 169.7 MB.
Total: 4.715958 ms (FindLiveObjects: 0.272084 ms CreateObjectMapping: 0.136917 ms MarkObjects: 3.314375 ms  DeleteObjects: 0.992417 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x32508b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.371 seconds
Refreshing native plugins compatible for Editor in 0.21 ms, found 1 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.433 seconds
Domain Reload Profiling: 807ms
	BeginReloadAssembly (106ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (225ms)
		LoadAssemblies (142ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (109ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (93ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (433ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (345ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (97ms)
			ProcessInitializeOnLoadAttributes (199ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.19 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6278 unused Assets / (3.8 MB). Loaded Objects now: 7084.
Memory consumption went from 173.2 MB to 169.4 MB.
Total: 4.442708 ms (FindLiveObjects: 0.299667 ms CreateObjectMapping: 0.126500 ms MarkObjects: 3.039208 ms  DeleteObjects: 0.977083 ms)

Prepare: number of updated asset objects reloaded= 0
